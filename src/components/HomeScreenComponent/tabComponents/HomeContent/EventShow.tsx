import auth from '@react-native-firebase/auth';
import {RouteProp, useFocusEffect, useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import {t} from 'i18next';
import moment from 'moment-timezone';
import React, {useEffect, useRef, useState} from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Image,
  Linking,
  NativeSyntheticEvent,
  Platform,
  Pressable,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TextLayoutEventData,
  TouchableOpacity,
  View,
} from 'react-native';
import RNCalendarEvents from 'react-native-calendar-events';
import FastImage from 'react-native-fast-image';
import MapView, {Marker, PROVIDER_GOOGLE} from 'react-native-maps';
import {Notifier, NotifierComponents} from 'react-native-notifier';
import {PERMISSIONS, RESULTS, check, request} from 'react-native-permissions';
import Animated, {
  FadeInDown,
  StretchInY,
  withTiming,
  useAnimatedStyle,
  useSharedValue,
  useAnimatedReaction,
  withSpring,
} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {LONG_DATE_FORMAT} from '~Utils/Time';
import {openLocationInMaps} from '~Utils/openLocationInMaps';
import {BigCheckIcon, BigPendingIcon, CheckIcon} from '~assets/icons';
import Button from '~components/Button';
import {GoBackHeader} from '~components/GoBackHeader';
import LikeButton from '~components/LikeButton';
import ModalWithJoin from '~components/ModalWithItems/ModalWithJoin';
import {SCREENS} from '~constants';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetBusinessAccount} from '~hooks/business/useGetBusinessAccount';
import {useAddUserEventStatus} from '~hooks/event/useAddUserEventStatus';
import {useDeleteEvent} from '~hooks/event/useDeleteEvent';
import {useDeleteUserEventStatus} from '~hooks/event/useDeleteUserEventStatus';
import {useGetEventAttendees} from '~hooks/event/useGetEventAttendees';
import {useGetEventById} from '~hooks/event/useGetEventById';
import {useGetEventUserStatus} from '~hooks/event/useGetEventUserStatus';
import useMatchingLoadingModalAnimation from '~hooks/react-hooks/useMatchingLoadingModalAnimation';
import {useGetUserAccount} from '~hooks/user/useGetUser';
import FirebaseChatsService from '~services/FirebaseChats';
import {Business} from '~types/api/business';
import {SUBSCRIPTION_STATUS, USER_EVENT_STATUS, User} from '~types/api/user';
import {NavigationProps, RootStackParamsList} from '~types/navigation/navigation.type';
import {getDetailStyles, getStyles} from '../../../../containers/Event/EventDetails/styles';
import * as AddCalendarEvent from 'react-native-add-calendar-event';
import firestore from '@react-native-firebase/firestore';
import QRCodeGenerator from '~components/QR-codes/QRCodeGenerator';
import ScannerQRCode from '~components/QR-codes/ScannerQRCode';
import ModalWithCalendar from '~components/ModalWithItems/ModalWithCalendar/ModalWithCalendar';
import EventDetailsPlaceholder from '../../../../containers/Event/EventDetails/EventDetailsPlaceholder';
import Hyperlink from 'react-native-hyperlink';
import Calendar from '~components/Calendar';
import {Dialog} from 'react-native-simple-dialogs';
import axios from 'axios';
import {
  BottomSheetModalProvider,
  BottomSheetModal,
  BottomSheetScrollView,
  useBottomSheetInternal,
} from '@gorhom/bottom-sheet';
import {useUpdateUserPostCode} from '~hooks/user/useUpdateUserPostcode';
import CommentSheet from '~components/CommentSheet';
import {useCancelEvent} from '~hooks/event/useCancelEvent';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useGetCommentByEvent} from '~hooks/event/useGetCommentByEvent';
import {useCreateComment} from '~hooks/event/useCreateComment';
import AnimatedLoadingModal from '~components/Matching/AnimatedLoadingModal';
import {useCreateShortUrl} from '~hooks/event/useCreateShortUrl';
import Share from 'react-native-share';
import {getMessageOfRecurrence} from '~Utils/event';
import {ChatType} from '~types/chat';
import {useGetEventMatchingStatus} from '~hooks/event/useGetEventMatchingStatus';
import Config from 'react-native-config';
import IssueModal from '~components/IssueModal';
import {useGetEventSubcategories} from '~hooks/event/useGetEventSubcategories';
import {useEventCreationStore} from '~providers/eventCreation/zustand';
import {Event} from '~types/api/event';
import {useTheme} from '~contexts/ThemeContext';
import {getMapStyle} from '~constants/locationMap';

const {height} = Dimensions.get('window');
const isAndroid = Platform.OS === 'android';
const TRUNCATE_DESCRIPTION_NUMBER_OF_LINES = 6;

const DATE_FORMAT = 'ddd ' + LONG_DATE_FORMAT;

const addEventToCalendar = async (title: any, startDate: any, endDate: any, description: any, location: any) => {
  const now = new Date();
  const startDateEvent = new Date(startDate);
  const effectiveStartDate = startDateEvent < now ? now : startDateEvent;

  try {
    const authStatus = await RNCalendarEvents.requestPermissions();
    if (authStatus === 'authorized') {
      const eventId = await RNCalendarEvents.saveEvent(title, {
        startDate: effectiveStartDate.toISOString(),
        endDate: new Date(endDate).toISOString(),
        description: description,
        location: location,
      });

      if (Platform.OS === 'ios') {
        AddCalendarEvent.presentEventViewingDialog({eventId: eventId})
          .then(event => {
            Alert.alert('Calendar', 'Your event has been saved.');
          })
          .catch(error => {
            console.error('Error presenting event viewing dialog', error);
            Alert.alert('Error', 'Failed to open the event viewer: ' + error.message);
          });
      } else {
        Alert.alert('Unsupported Feature', 'This feature is only available on iOS.');
      }
    } else {
      console.error('Calendar permission is not authorized');
      return;
    }
  } catch (error) {
    console.error('Failed to add event to calendar:', error);
  }
};

const addCalendarEvent = async (eventConfig: any) => {
  try {
    const eventInfo = await AddCalendarEvent.presentEventCreatingDialog(eventConfig);
    setTimeout(() => {
      if (eventInfo?.action !== 'CANCELED') {
        Alert.alert('Calendar', 'Your event has been saved.');
      } else {
        Alert.alert('Calendar', 'Your event has been cancelled.');
      }
    }, 2000);
  } catch (error) {
    Alert.alert('Error', 'Failed to add event to calendar');
    console.error('Add event error:', error);
  }
};

const getRandomItem = (arr: Array<any>) => {
  return arr[Math.floor(Math.random() * arr.length)];
};

const randomUserArray1 = [
  require('../../../../assets/images/user1.png'),
  require('../../../../assets/images/user2.png'),
  require('../../../../assets/images/user3.png'),
  require('../../../../assets/images/user4.png'),
  require('../../../../assets/images/user5.png'),
  require('../../../../assets/images/user6.png'),
  require('../../../../assets/images/user7.png'),
  require('../../../../assets/images/user8.png'),
];

const randomUserArray2 = [
  require('../../../../assets/images/user9.png'),
  require('../../../../assets/images/user10.png'),
  require('../../../../assets/images/user11.png'),
  require('../../../../assets/images/user12.png'),
  require('../../../../assets/images/user13.png'),
  require('../../../../assets/images/user14.png'),
  require('../../../../assets/images/user15.png'),
];

// Helper functions for event type styling
const getEventTypeColor = (eventType: string) => {
  switch (eventType) {
    case 'business':
      return '#4A48AD';
    case 'influencer':
      return '#F5A865';
    case 'community':
      return '#10B981';
    case 'pyxi_select':
      return '#FF6B6B';
    default:
      return '#4A48AD';
  }
};

const getEventTypeLabel = (eventType: string) => {
  switch (eventType) {
    case 'business':
      return 'Business';
    case 'influencer':
      return 'Influencer';
    case 'community':
      return 'Community';
    case 'pyxi_select':
      return 'Pyxi Select';
    default:
      return 'Event';
  }
};

export function EventShow(props: {
  eventId: number;
  tag?: string;
  statusTag?: string;
  item?: Event;
  sheetPosition: number;
}) {
  const {colors, isDarkMode} = useTheme();
  const styles = getStyles(colors);
  const detailStyles = getDetailStyles(colors);
  const {top} = useSafeAreaInsets();
  const calendarRef = React.useRef<{open: () => void; close: () => void}>();
  const navigation = useNavigation<NavigationProps>();
  const commentSheetRef = useRef<BottomSheetModal>(null);
  const {tag, eventId, item: itemFromRoute} = props;
  const [descriptionNumberOfLines, setDescriptionNumberOfLines] = React.useState(TRUNCATE_DESCRIPTION_NUMBER_OF_LINES);
  const [lengthMore, setLengthMore] = React.useState(false);
  const [modalIsVisible, setModalIsVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const {data: pyxiHost} = useGetBusinessAccount('wLJLEn8J6oN9RpPyep2BjdnagcA2');

  const {data: fetchedItem, refetch: refetchEvent, isLoading: isEventLoading} = useGetEventById(eventId);
  const {data: matchingStatus, refetch: refetchMatchinEventSatus} = useGetEventMatchingStatus(eventId + '');
  const {
    data: commentList,
    refetch: refetchCommentEvent,
    isLoading: isCommentEventLoading,
  } = useGetCommentByEvent(eventId);
  const {mutateAsync: createComment} = useCreateComment();
  const {data: eventAttendees, refetch: refetchAttendees} = useGetEventAttendees({
    event_id: eventId,
    subscription_status: SUBSCRIPTION_STATUS.ACCEPTED,
  });

  const {
    data: userStatus,
    isLoading: userStatusIsLoading,
    isFetching: userStatusIsFetching,
    refetch,
  } = useGetEventUserStatus({event_id: eventId, user_id: auth()!.currentUser?.uid || ''});

  const {mutateAsync: addEventUserStatus} = useAddUserEventStatus();
  const {openMatchingLoadingModal, setCurrentMatchingEvent, setDomain, setRefresh} = useMatchingLoadingModalAnimation();
  const {data: userAccount, refetch: refetchUser} = useGetUserAccount(auth().currentUser?.uid);
  const {data: businessAccount, refetch: refetchBusiness} = useGetBusinessAccount(auth().currentUser?.uid || '');

  // console.log('userAccount', userAccount);
  const isUserIsAttendee = eventAttendees?.some(attendee => attendee.user?.uid === userAccount?.uid) || false;

  const item = fetchedItem || itemFromRoute;
  const user = !(userAccount as unknown as {detail: string})?.detail ? userAccount : businessAccount;

  const publicDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];

  const userDomain = user?.email?.split('@')[1];
  const isPublicDomain = publicDomains.includes(userDomain || '');

  const [eventCountry, setEventCountry] = useState('');
  const [pincodeDialogVisible, setPinCodeDialog] = useState(false);
  const [postCode, setPostCode] = useState('');
  // console.log('user', user);

  const {data: hostUser} = useGetUserAccount(item?.host_id || '');
  const {data: hostBusiness} = useGetBusinessAccount(item?.host_id || '');
  const {mutateAsync: deleteEvent} = useDeleteEvent();
  const {mutateAsync: cancelAEvent} = useCancelEvent();
  const {mutateAsync: deleteEventUserStatus} = useDeleteUserEventStatus();
  const {mutateAsync: updateUserMutation} = useUpdateUserPostCode();

  const [isLoading, setIsLoading] = useState(false);
  const [spinner, setIsSpinner] = useState(false);
  const [isAnimationVisible, setAnimationVisisble] = useState(false);
  const {createShortUrl, isLoading: shareEventLoading} = useCreateShortUrl();

  const {data: categoryData} = useGetEventSubcategories(eventId);
  const {setSubCategory} = useEventCreationStore();
  const isEventPassed = moment(item?.end_date).isBefore(moment());
  const [randomUserImg1, serRandomUserImg1] = useState<any>();
  const [randomUserImg2, serRandomUserImg2] = useState<any>();

  const imageHeight = useSharedValue(300); // Start with full height
  const imageOpacity = useSharedValue(1); // Start with full opacity
  const imageScale = useSharedValue(1); // Start with full scale
  const {animatedPosition} = useBottomSheetInternal();

  useEffect(() => {
    serRandomUserImg1(getRandomItem(randomUserArray1));
    serRandomUserImg2(getRandomItem(randomUserArray2));
  }, []);

  useAnimatedReaction(
    () => animatedPosition.value,
    (position, prevPosition) => {
      if (position !== prevPosition) {
        // Calculate the percentage of sheet position (0 to 1)
        const sheetHeight = height * 0.85; // Assuming sheet takes 85% of screen height
        const positionPercentage = 1 - position / sheetHeight; // Reverse the percentage

        // If position is less than 30% of sheet height (sheet is pulled down), hide image
        if (positionPercentage < 0.3) {
          imageHeight.value = withSpring(0, {
            damping: 15,
            stiffness: 100,
            mass: 0.5,
          });
          imageOpacity.value = withSpring(0, {
            damping: 15,
            stiffness: 100,
            mass: 0.5,
          });
          imageScale.value = withSpring(0.8, {
            damping: 15,
            stiffness: 100,
            mass: 0.5,
          });
        } else {
          // Interpolate image height based on position
          const newHeight = Math.max(150, 300 * positionPercentage);
          imageHeight.value = withSpring(newHeight, {
            damping: 15,
            stiffness: 100,
            mass: 0.5,
          });
          imageOpacity.value = withSpring(0.8 + positionPercentage * 0.2, {
            damping: 15,
            stiffness: 100,
            mass: 0.5,
          });
          imageScale.value = withSpring(0.8 + positionPercentage * 0.2, {
            damping: 15,
            stiffness: 100,
            mass: 0.5,
          });
        }
      }
    },
    [],
  );

  const imageStyle = useAnimatedStyle(() => {
    return {
      height: imageHeight.value,
      width: '100%',
      opacity: imageOpacity.value,
      transform: [{scale: imageScale.value}],
    };
  });

  const isFocused = useIsFocused();

  const [coords, setCoords] = useState({
    latitude: item ? item.coords.lat : 51.5072178, // Default latitude (e.g., London)
    longitude: item ? item.coords.long : -0.1275862, // Default longitude (e.g., London)
  });
  const [address, setAddress] = useState('Fetching address...');
  const [region, setRegion] = useState({
    latitude: 51.5072178,
    longitude: -0.1275862,
    latitudeDelta: 1,
    longitudeDelta: 1,
  });

  useEffect(() => {
    if (isFocused) {
      refetchMatchinEventSatus();
      refetchAttendees();
      refetchEvent();
      refetch();
      refetchUser();
      refetchBusiness();
    }
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    if (item?.coords?.lat && item?.coords?.long) {
      const newCoords = {
        latitude: item.coords.lat,
        longitude: item.coords.long,
      };
      setCoords(newCoords);
      setRegion(prev => ({
        ...prev,
        latitude: newCoords.latitude,
        longitude: newCoords.longitude,
      }));
      if (item.coord_address) {
        setAddress(item.coord_address);
      }
    }
  }, [item]);

  useEffect(() => {
    // Fetch address using reverse geocoding whenever coordinates change
    const fetchAddress = async () => {
      try {
        const apiKey = Config.GOOGLE_API_KEY; // Get the API key from your config
        const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${coords.latitude},${coords.longitude}&key=${apiKey}&language=en`;

        // Perform the GET request using axios
        const response = await axios.get(url);

        // Check if we got valid results
        if (response.data.results && response.data.results.length > 0) {
          setAddress(response.data.results[0].formatted_address); // Set the address from the first result
        } else {
          setAddress('Address not found');
        }
      } catch (error) {
        // Handle any errors (e.g., network errors, invalid response, etc.)
        console.error('Error fetching address:', error);
        setAddress('Error fetching address');
      }
    };

    fetchAddress();
  }, [coords]);

  useEffect(() => {
    if (item?.coords?.lat && item?.coords?.long) {
      const newCoords = {
        latitude: item.coords.lat,
        longitude: item.coords.long,
      };
      setCoords(newCoords);
      if (item.coord_address) {
        setAddress(item.coord_address);
      }
    }
  }, [item]);

  useEffect(() => {
    if (eventId) {
      refetchMatchinEventSatus();
      refetchAttendees();
      refetchEvent();
      refetch();
    }
    setIsLoading(false);
  }, [eventId]);

  useEffect(() => {
    if (Platform.OS === 'android') {
      request(PERMISSIONS.ANDROID.READ_CALENDAR).then(result => {
        console.log(`android calendar read permission: ${result}`);
      });
    }
  }, []);

  const getCountryFromLatLng = async () => {
    const response = await axios.get(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${fetchedItem?.coords.lat},${fetchedItem?.coords.long}&key=AIzaSyAQMLxSRIygFhqS9dazo2HlJXs3rLrld-Q`,
    );

    const result = response.data.results[0];
    const country = result.address_components.find((component: any) => component.types.includes('country')).long_name;
    setEventCountry(country);
  };

  useEffect(() => {
    if (fetchedItem?.coords.lat) {
      getCountryFromLatLng();
    }
  }, [fetchedItem?.coords.lat]);

  const onNotificationAboutSubscriptionOn = async (item: any, host: any, responseData: any) => {
    setTimeout(async () => {
      if (responseData.status === USER_EVENT_STATUS.PENDING) {
        Notifier.showNotification({
          title: 'Please wait for the host to accept your request.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'info',
          },
        });
        return;
      } else if (responseData.status === 'waiting') {
        Notifier.showNotification({
          title: 'You are in waitlist. Please wait for the host to accept your request.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'info',
          },
        });
      } else {
        Notifier.showNotification({
          title: 'All Set! You are Ready for the event.',
          Component: NotifierComponents.Alert,
          componentProps: {
            alertType: 'success',
          },
        });
        refetchAttendees();
        if (item?.host_id != 'wLJLEn8J6oN9RpPyep2BjdnagcA2') {
          setAnimationVisisble(true);
        }
      }
    }, 2000);
  };

  const onNotificationAboutSubscriptionOff = async (item: any, host: any) => {
    setTimeout(async () => {
      console.log('Notification Off'); // Додано логування
      Notifier.showNotification({
        title: "We notified the organiser you're no longer going to the event.",
        Component: NotifierComponents.Alert,
        componentProps: {
          alertType: 'success',
        },
      });
      try {
        console.log('Fetching event owner data for host UID:', host.uid); // Додано логування
        // Отримуємо FCM token власника події
        const eventOwnerDoc = await firestore().collection('users').doc(host.uid).get();
        const eventOwnerData = eventOwnerDoc.data();
        console.log('Fetched event owner data:', eventOwnerData); // Додано логування
        if (eventOwnerData && eventOwnerData.deviceFcmToken) {
          console.log('Sending push notification to:', eventOwnerData.deviceFcmToken); // Додано логування
          // Відправляємо сповіщення
          await FirebaseChatsService.sendPushNotification(
            eventOwnerData.deviceFcmToken,
            'New Event Notification',
            `User ${userAccount?.first_name} are no longer going to the event ${item?.name}`,
            {
              clickAction: 'OPEN_NOTIFICATIONS',
              eventId: item.event_id,
              item: JSON.stringify(item), // Передаємо дані item у сповіщенні
            },
          );
        } else {
          console.log('Host does not have an FCM token.');
        }
      } catch (error) {
        console.error('Error fetching event owner data:', error);
      }
    }, 2000);
  };
  // console.log('fetchedItem', fetchedItem);

  const host = !(hostUser as unknown as {detail: string})?.detail ? hostUser : hostBusiness;
  // console.log('host', host);

  const {setIsTabBarDisabled} = useTabBar();
  const mapRef = React.useRef<MapView>(null);
  const goBackHandler = () => {
    setIsTabBarDisabled(false);
    navigation.goBack();
  };

  React.useLayoutEffect(() => {
    setIsTabBarDisabled(true);

    return () => {
      setIsTabBarDisabled(false);
    };
  }, [setIsTabBarDisabled]);

  useEffect(() => {
    logScreenView('Event Detail', 'EventDetail');
  }, []);

  const isUserEvent = item?.host_id === auth().currentUser?.uid;

  const handleEditEvent = () => {
    if (!item) {
      return;
    }
    if (categoryData) {
      categoryData.forEach(cate_id => {
        setSubCategory(cate_id.subcategory_id);
      });
    }
    navigation.navigate(SCREENS.CREATE_EVENT_TEMPLATE, {item});
  };

  const submitDelete = () =>
    Alert.alert('Are you sure you want to delete this event?', '', [
      {
        text: 'No',
        style: 'cancel',
      },
      {text: 'Yes', onPress: () => handleDeleteSubmitted(), style: 'destructive'},
    ]);

  const handleDeleteSubmitted = async () => {
    setIsLoading(true);
    if (!item?.event_id) {
      return;
    }
    await deleteEvent({event_id: item.event_id});
    try {
      await FirebaseChatsService.deleteGroupChat({event_id: item.event_id});
    } catch (error) {}
    navigation.goBack();
  };

  const cancelEvent = async () => {
    setIsLoading(true);
    if (!item?.event_id) {
      return;
    }
    await cancelAEvent({event_id: item.event_id});
    refetchEvent();
    setIsLoading(false);
  };

  const userName = (user as User)?.last_name
    ? (user as User)?.first_name + ' ' + (user as User)?.last_name
    : (user as User)?.first_name || (user as Business)?.name;

  const handleSharePress = async () => {
    if (!item?.event_id) {
      return;
    }
    const redirectLink = `https://partner.pyxi.ai/event/detail/${item.event_id}`;
    const url = redirectLink;
    await Share.open({
      message: t('invite_message', {userName, itemName: item.name, url}),
    });
  };

  const handleOpenMaps = () => {
    if (!item?.coords?.lat || !item?.coords?.long) {
      return;
    }
    openLocationInMaps({lat: item.coords.lat, long: item.coords.long, pointLabel: item?.address_name});
  };

  const onTextLayout = React.useCallback((e: NativeSyntheticEvent<TextLayoutEventData>) => {
    //to check should truncate or not
    setLengthMore(e.nativeEvent.lines.length >= TRUNCATE_DESCRIPTION_NUMBER_OF_LINES);
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      refetchEvent();
      refetchAttendees(); // Додаємо рефетч для учасників
    }, [refetchEvent, refetchAttendees]),
  );

  const onDatePress = () => {
    calendarRef.current?.open();
  };

  if (!itemFromRoute && isEventLoading) {
    return <EventDetailsPlaceholder />;
  }
  // if (!itemFromRoute && isEventLoading) {
  //   return <EventDetailsPlaceholder />;
  // }

  const addToCalendar = () => {
    if (Platform.OS == 'ios') {
      addEventToCalendar(item?.name, item?.start_date, item?.end_date, item?.description, item?.address_name);
    } else {
      const eventConfig = {
        title: item?.name,
        startDate: new Date(item?.start_date || '').toISOString(),
        endDate: new Date(item?.end_date || '').toISOString(),
        location: item?.address_name,
        notes: item?.description,
      };

      addCalendarEvent(eventConfig);
    }
  };

  const addJoinEventStatus = async () => {
    setModalIsVisible(false);
    await addEventUserStatus({
      event_id: eventId.toString(),
    });
  };

  const onJoinEventClick = () => {
    if (!userStatus && eventCountry === 'Greece') {
      if (userAccount?.postal_code) {
        joinEvent();
      } else {
        setPinCodeDialog(true);
      }
    } else {
      joinEvent();
    }
  };

  const onJoinEvent = async (event_id: number, isLeave: boolean) => {
    setIsSpinner(true);
    await joinEvent(event_id, isLeave);
    setIsSpinner(false);
  };

  const onAddComment = async (comment: string) => {
    const reqBody = {
      comment: comment,
      pin: false,
      parent_id: null,
      event_id: item?.event_id || 0,
    };
    await createComment(reqBody);
    refetchCommentEvent();
  };

  const joinEvent = async (event_id?: number, isLeave?: boolean) => {
    if (!user?.onboarding_answers || user.onboarding_answers.length === 0) {
      navigation.navigate(SCREENS.EDIT_PREFERANCE, {setting: true});
      return;
    }
    if (item?.event_group_id && !event_id) {
      onDatePress();
      return;
    }
    if (item && !item?.payment_url) {
      if (userStatus?.status !== USER_EVENT_STATUS.ACCEPTED) {
        navigation.navigate(SCREENS.BUY_TICKET, {eventId: item.event_id});
        return;
      } else {
        navigation.navigate(SCREENS.PAYMENT_SUCCESS, {order_id: userStatus.order_id + ''});
        return;
      }
    }

    if (item?.payment_url && userStatus?.status !== USER_EVENT_STATUS.ACCEPTED) {
      setModalIsVisible(true);
    }

    if (!item) {
      return;
    }
    if (event_id) {
      if (!isLeave) {
        const data = await addEventUserStatus({
          event_id: event_id.toString(),
        });
        if (!item?.is_paid) {
          userStatus?.status === USER_EVENT_STATUS.ACCEPTED
            ? onNotificationAboutSubscriptionOff(item, host)
            : onNotificationAboutSubscriptionOn(item, host, data);
        }

        if (item.event_type === 'business' || item.private) {
          await FirebaseChatsService.addNewUserToTheGroupChat({
            user_id: auth().currentUser!.uid,
            user_name: `${(user as User)?.first_name || ''} ${(user as User)?.last_name || ''}`,
            user_image: user!.photo,
            event_id: event_id,
          });
        }
      } else {
        await deleteEventUserStatus({event_id: event_id, user_id: auth().currentUser!.uid});
        await FirebaseChatsService.removeUserFromTheGroupChat({
          user_id: auth().currentUser!.uid,
          user_name: `${(user as User)!.first_name || ''} ${(user as User)!.last_name || ''}`,
          event_id: event_id,
          event_image: item.image_url,
        });
      }
    } else {
      if (!userStatus) {
        const data = await addEventUserStatus({
          event_id: eventId.toString(),
        });
        if (!item?.is_paid) {
          // @ts-ignore
          userStatus?.status === USER_EVENT_STATUS.ACCEPTED
            ? onNotificationAboutSubscriptionOff(item, host)
            : onNotificationAboutSubscriptionOn(item, host, data);
        }

        if (item.event_type === 'business' || item.private) {
          await FirebaseChatsService.addNewUserToTheGroupChat({
            user_id: auth().currentUser!.uid,
            user_name: `${(user as User)?.first_name || ''} ${(user as User)?.last_name || ''}`,
            user_image: user!.photo,
            event_id: item.event_id,
          });
        }
      } else {
        await deleteEventUserStatus({event_id: item.event_id, user_id: auth().currentUser!.uid});
        await FirebaseChatsService.removeUserFromTheGroupChat({
          user_id: auth().currentUser!.uid,
          user_name: `${(user as User)!.first_name || ''} ${(user as User)!.last_name || ''}`,
          event_id: item.event_id,
          event_image: item.image_url,
        });
      }
    }
    if (event_id) {
      if (event_id !== item.event_id) {
        navigation.replace(SCREENS.HOME_EVENT, {eventId: event_id});
      } else {
        await refetch();
      }
    } else {
      await refetch();
    }
  };
  const onContinuePress = async () => {
    if (!postCode && postCode.length < 3) {
      Alert.alert('Please enter postal code.');
    }

    setPinCodeDialog(false);

    const data = await updateUserMutation(postCode);
    if (data.postal_code) {
      joinEvent();
    }
  };
  const onCommentPress = () => {
    commentSheetRef.current?.present();
  };
  const getAttendeesCount = () => {
    let totalAttendees = 1;
    if (eventAttendees && eventAttendees.length > 0) {
      totalAttendees = 1 + eventAttendees.length;
    }
    if (totalAttendees > 2) {
      totalAttendees = totalAttendees - 2;
    }
    return totalAttendees;
  };
  const getLinkOf1stAttendeesProfile = () => {
    if (eventAttendees && eventAttendees.length > 0 && eventAttendees[0].user && eventAttendees[0].user.photo) {
      return eventAttendees[0].user.photo;
    }
    return null;
  };
  const getLinkOf2ndAttendeesProfile = () => {
    if (eventAttendees && eventAttendees.length > 1 && eventAttendees[1].user && eventAttendees[1].user.photo) {
      return eventAttendees[1].user.photo;
    }
    return null;
  };
  const onQrPress = () => {
    navigation.navigate(SCREENS.PAYMENT_SUCCESS, {order_id: userStatus?.order_id + ''});
  };
  const onHostChatClick = async (host: Business) => {
    setModalVisible(true);
  };

  const onIssueTypeClick = async (type: string) => {
    const hHost = type == 't' ? pyxiHost : host;
    if (hHost && item) {
      const chatId = await FirebaseChatsService.createOrganisationChat({
        user_id1: auth().currentUser!.uid,
        user_id2: hHost?.uid,
        user_name1: `${userAccount?.first_name} ${userAccount?.last_name || ''}`,
        user_name2: (hHost as Business)?.name
          ? (hHost as Business)?.name
          : (hHost as unknown as User)?.last_name
            ? `${(hHost as unknown as User)?.first_name} ${(hHost as unknown as User)?.last_name || ''}`
            : (hHost as unknown as User)?.first_name || '',
        user_image: userAccount?.photo + '',
        event: item,
        isTechnical: type == 't' ? true : false,
      });

      const chatRef = firestore().collection('chats').doc(chatId);
      const doc = await chatRef.get();
      if (doc.exists) {
        const chatData = doc.data() as ChatType;
        const updatedMessages = chatData.history.map((message: any) => {
          if (!message.readUserIds?.includes(auth().currentUser!.uid)) {
            console.log('Updating message:', message);
            return {...message, readUserIds: [...(message.readUserIds || []), auth().currentUser!.uid]};
          }
          return message;
        });

        await chatRef.update({history: updatedMessages});
      }

      navigation.navigate(SCREENS.CHAT_STACK, {key: chatId});
    }
  };
  return (
    <BottomSheetModalProvider>
      {spinner && (
        <View style={styles.spinnerView}>
          <ActivityIndicator size={'large'} color={colors.eventInfluencer} />
        </View>
      )}
      {isUserEvent && false && (
        <Button
          label={t('events.delete_event')}
          isLoading={isLoading}
          onPress={() => submitDelete()}
          textStyle={{color: colors.white}}
          containerStyle={{
            position: 'absolute',
            right: 16,
            top: isAndroid ? top + 30 : top,
            zIndex: 1000,
            backgroundColor: colors.error,
            paddingVertical: 8,
          }}
        />
      )}

      <BottomSheetScrollView style={detailStyles.background} bounces={false}>
        <Animated.View style={[detailStyles.imageWrapper, imageStyle]}>
          <FastImage
            style={StyleSheet.absoluteFillObject}
            resizeMode={'cover'}
            source={{
              uri: item?.image_url,
              priority: 'high',
            }}
          />
          <Animated.Image
            sharedTransitionTag={tag}
            source={{uri: item?.image_url}}
            resizeMode={'cover'}
            style={[detailStyles.image, imageStyle]}
          />

          {/* Modern overlay gradient for better text readability */}
          <View style={styles.imageOverlay} />

          {/* Event type badge */}
          {item?.event_type && (
            <Animated.View
              style={[styles.eventTypeBadge, {backgroundColor: getEventTypeColor(item.event_type)}]}
              entering={FadeInDown.delay(100)}>
              <Text style={styles.eventTypeBadgeText}>{getEventTypeLabel(item.event_type)}</Text>
            </Animated.View>
          )}

          {!isUserEvent && (
            <View style={styles.likeContainer}>
              <LikeButton liked={!!item?.user_liked} eventId={item?.event_id} />
            </View>
          )}

          {isUserEvent &&
            item?.host_id != 'wLJLEn8J6oN9RpPyep2BjdnagcA2' &&
            item?.event_type === 'business' &&
            false && <ScannerQRCode user={eventAttendees} event={fetchedItem} />}
          {!isUserEvent &&
            isUserIsAttendee &&
            item?.host_id != 'wLJLEn8J6oN9RpPyep2BjdnagcA2' &&
            item?.event_type === 'business' && (
              <View style={styles.likeContainer}>
                <QRCodeGenerator onQrPress={onQrPress} user={userAccount} event={fetchedItem} />
              </View>
            )}
        </Animated.View>
        <View style={[detailStyles.container, props.sheetPosition == 0 && {paddingTop: 0}]}>
          <View>
            <View style={[detailStyles.priceWrapper, detailStyles.margin]}>
              <Animated.Text style={detailStyles.header} entering={FadeInDown.delay(200)}>
                {item?.name}
              </Animated.Text>
            </View>
            <Pressable
              onPress={() =>
                setDescriptionNumberOfLines(prevState => (prevState ? 0 : TRUNCATE_DESCRIPTION_NUMBER_OF_LINES))
              }>
              <Hyperlink
                onPress={url => Linking.openURL(url)}
                linkStyle={[detailStyles.description, detailStyles.linkColor]}>
                <Animated.Text
                  onTextLayout={onTextLayout}
                  style={detailStyles.description}
                  entering={FadeInDown.delay(300)}
                  numberOfLines={descriptionNumberOfLines}>
                  {item?.description}
                </Animated.Text>
              </Hyperlink>
              {lengthMore ? (
                <Text style={[detailStyles.description, detailStyles.readMoreTest]}>
                  {descriptionNumberOfLines ? 'Read more...' : 'Read less...'}
                </Text>
              ) : null}
            </Pressable>
          </View>
          {((commentList && commentList?.length > 0) || isUserEvent) && <View style={styles.divider} />}
          {commentList && commentList?.length > 0 ? (
            <TouchableOpacity onPress={onCommentPress} style={styles.commentView}>
              <Text style={styles.commentTitle}>Updates {commentList.length}</Text>
              <View style={styles.commentItemView}>
                <Image source={{uri: commentList[0].user.photo}} style={styles.userImage} />
                <Text style={styles.commentTextView}>{commentList[commentList.length - 1].comment}</Text>
              </View>
            </TouchableOpacity>
          ) : (
            isUserEvent && (
              <TouchableOpacity onPress={onCommentPress} style={styles.commentView}>
                <Text style={styles.commentTitle}>Updates</Text>
                <View style={[styles.commentItemView, {alignItems: 'center'}]}>
                  <Image source={{uri: user?.photo}} style={styles.userImage} />
                  <Text style={styles.commentTextView}>Add updates here</Text>
                </View>
              </TouchableOpacity>
            )
          )}
          {((commentList && commentList?.length > 0) || isUserEvent) && <View style={styles.divider} />}
          <View style={{flexDirection: 'row', flex: 1, justifyContent: 'space-between'}}>
            <View style={{flex: 1}}>
              <View style={[styles.checkContainer, {flex: 1}]}>
                <CheckIcon />
                <View style={styles.checkRightContainer}>
                  <Text style={styles.checkLabel}>Date & Time</Text>
                  <View style={{flex: 1}}>
                    <Text style={styles.checkDescription}>
                      {item?.start_date ? moment.utc(item?.start_date).format(DATE_FORMAT) : 'N/A'}
                      {item?.end_date ? ' -' : ''}
                    </Text>
                    {item?.end_date && (
                      <Text style={styles.checkDescription}>{moment.utc(item?.end_date).format(DATE_FORMAT)}</Text>
                    )}
                  </View>
                </View>
              </View>

              <TouchableOpacity style={{...styles.checkContainer, marginTop: 16}} onPress={handleOpenMaps}>
                <CheckIcon />
                <View style={styles.checkRightContainer}>
                  <Text style={styles.checkLabel}>{'Location'}</Text>
                  <Text style={styles.checkDescription}>{item?.address_name || item?.coord_address}</Text>
                </View>
              </TouchableOpacity>

              {item?.event_group_id && (
                <View style={{...styles.checkContainer, marginTop: 16}}>
                  <CheckIcon />
                  <View style={styles.checkRightContainer}>
                    <Text style={styles.checkLabel}>{'Recurrence'}</Text>
                    <Text style={styles.checkDescription}>{getMessageOfRecurrence(item?.event_group)}</Text>
                  </View>
                </View>
              )}
            </View>
            {item?.host_id !== auth().currentUser?.uid && !userStatusIsLoading && !userStatusIsFetching ? (
              <View
                key={userStatus?.status || 'statusButton'}
                // sharedTransitionTag={statusTag}
                style={{aspectRatio: 1, width: 100, alignSelf: 'center'}}>
                {!isEventPassed || userStatus?.status ? (
                  <Button
                    disabled={
                      userStatus?.status === USER_EVENT_STATUS.PENDING ||
                      item?.is_cancelled ||
                      moment(item?.end_date).isBefore(moment())
                    }
                    icon={
                      userStatus?.status === USER_EVENT_STATUS.ACCEPTED ? (
                        <BigCheckIcon />
                      ) : userStatus?.status === USER_EVENT_STATUS.PENDING ? (
                        <BigPendingIcon />
                      ) : undefined
                    }
                    label={
                      userStatus?.status === USER_EVENT_STATUS.ACCEPTED
                        ? 'Accepted'
                        : userStatus?.status === USER_EVENT_STATUS.PENDING
                          ? 'Pending'
                          : userStatus?.status === 'waiting'
                            ? 'Waitlisted'
                            : item?.is_paid || item?.payment_url
                              ? 'Buy tickets'
                              : 'Join'
                    }
                    containerStyle={{
                      backgroundColor: item?.is_cancelled
                        ? colors.statusGray
                        : userStatus?.status === USER_EVENT_STATUS.ACCEPTED
                          ? colors.statusGreen
                          : userStatus?.status === USER_EVENT_STATUS.PENDING
                            ? colors.primary
                            : userStatus?.status === 'waiting'
                              ? colors.primary
                              : colors.statusPurple,
                      marginTop: 12,
                      flex: 1,
                      borderRadius: 20,
                      paddingVertical: undefined,
                    }}
                    textStyle={{
                      fontSize: userStatus?.status === 'waiting' ? 12 : 16,
                      fontWeight: '600',
                      textAlign: 'center',
                      color: colors.white,
                      lineHeight: 20,
                    }}
                    onPress={async () => onJoinEventClick()}
                  />
                ) : (
                  <Button
                    label={'Buy tickets'}
                    containerStyle={{
                      backgroundColor: colors.gray400,
                      marginTop: 12,
                      flex: 1,
                      borderRadius: 20,
                      paddingVertical: undefined,
                    }}
                    textStyle={{
                      fontSize: 14,
                      fontWeight: '600',
                      textAlign: 'center',
                      color: colors.white,
                      lineHeight: 20,
                    }}
                    onPress={async () => Alert.alert('The event has ended')}
                  />
                )}
              </View>
            ) : (
              (userStatusIsLoading || userStatusIsFetching) && (
                <View style={styles.loaderJoinBtnView}>
                  <ActivityIndicator />
                </View>
              )
            )}
          </View>

          <View style={styles.divider} />

          {!fetchedItem ? (
            <></>
          ) : fetchedItem?.is_cancelled ? (
            <>
              <Text style={styles.errorText}>
                {fetchedItem?.event_type === 'business'
                  ? 'This event was cancelled by the organiser'
                  : 'This event was cancelled by host.'}
              </Text>
              {isUserEvent && (
                <Button
                  label={t('events.update_event')}
                  containerStyle={{backgroundColor: colors.primary, marginTop: 10}}
                  textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                  onPress={handleEditEvent}
                />
              )}
            </>
          ) : (
            <>
              {isUserEvent ? (
                <Button
                  label={t('events.update_event')}
                  containerStyle={{backgroundColor: colors.primary}}
                  textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                  onPress={handleEditEvent}
                />
              ) : (
                <View style={{flexDirection: 'column', justifyContent: 'space-between', marginTop: 10}}>
                  <Button
                    label={
                      matchingStatus?.matches_available
                        ? matchingStatus?.type === 'all'
                          ? t('events.see_my_match')
                          : t('home.find_someone')
                        : t('home.find_someone')
                    }
                    containerStyle={{
                      backgroundColor: matchingStatus?.matches_available ? colors.statusGreen : colors.statusPurple,
                    }}
                    textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                    onPress={() => {
                      if (false && userStatus?.status != USER_EVENT_STATUS.ACCEPTED) {
                        Alert.alert('Note!', 'Please join the event before searching for someone to go with.');
                        return;
                      }
                      if (matchingStatus?.matches_available && matchingStatus?.type === 'colleague') {
                        Alert.alert(
                          'Note!',
                          "If you find someone to go with, your current colleague matches will be reset. However, you'll be able to find them again later. Are you sure you want to proceed?",
                          [
                            {
                              text: 'No',
                              onPress: () => {
                                // If user cancels, close the dialog and do nothing
                              },
                              style: 'cancel',
                            },
                            {
                              text: 'Yes',
                              onPress: () => {
                                setCurrentMatchingEvent(item?.event_id || null);
                                setDomain(null);
                                setRefresh(true);
                                openMatchingLoadingModal();
                              },
                            },
                          ],
                        );
                      } else {
                        setCurrentMatchingEvent(item?.event_id || null);
                        setDomain(null);
                        setRefresh(false);
                        openMatchingLoadingModal();
                      }
                    }}
                  />
                  {/* Render the second button only if the domain is not public */}
                  {(host as Business) && (host as Business)!.user_pooling! && (
                    <Button
                      label={
                        matchingStatus?.matches_available
                          ? matchingStatus?.type === 'colleague'
                            ? 'See My Colleagues'
                            : 'Find My Colleagues'
                          : 'Find My Colleagues'
                      }
                      containerStyle={{
                        backgroundColor: colors.primary,
                        marginTop: 10,
                      }}
                      textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                      onPress={() => {
                        if (matchingStatus?.matches_available && matchingStatus?.type === 'all') {
                          // Show the confirmation dialog
                          Alert.alert(
                            'Note!',
                            "If you find my colleagues, your current matches will be reset. However, you'll be able to find them again later. Are you sure you want to proceed?",
                            [
                              {
                                text: 'No',
                                onPress: () => {
                                  // If user cancels, close the dialog and do nothing
                                },
                                style: 'cancel',
                              },
                              {
                                text: 'Yes',
                                onPress: () => {
                                  // If user confirms, proceed with the action
                                  setCurrentMatchingEvent(item?.event_id || null);
                                  setDomain(userDomain || '');
                                  setRefresh(true);
                                  openMatchingLoadingModal();
                                },
                              },
                            ],
                          );
                        } else {
                          // If the condition is not 'all', proceed without dialog
                          setCurrentMatchingEvent(item?.event_id || null);
                          setDomain(userDomain || '');
                          setRefresh(false);
                          openMatchingLoadingModal();
                        }
                      }}
                    />
                  )}
                </View>
              )}

              {isUserEvent && false && (
                <Button
                  label={t('events.cancel_event')}
                  containerStyle={{backgroundColor: colors.primary, marginTop: 10}}
                  textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                  onPress={() => cancelEvent()}
                  isLoading={isLoading}
                />
              )}

              <Button
                label={t('home.share')}
                containerStyle={{
                  backgroundColor: colors.white,
                  marginTop: 12,
                  flex: 1,
                  width: undefined,
                  paddingVertical: 10,
                  borderWidth: 1,
                  borderColor: colors.statusPurple,
                }}
                spinnerColor={colors.statusPurple}
                isLoading={shareEventLoading}
                textStyle={{fontWeight: '500', color: colors.statusPurple}}
                onPress={handleSharePress}
              />

              <Button
                label="Add to my calendar"
                containerStyle={{backgroundColor: colors.primary, marginTop: 10}}
                textStyle={{fontSize: 16, fontWeight: '600', color: colors.white}}
                onPress={() => addToCalendar()}
              />
            </>
          )}

          <View style={styles.divider} />

          {!!host && (
            <>
              <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
                <Text style={{fontSize: 18, lineHeight: 20, fontWeight: '600'}}>{t('events.host')}</Text>
                {isUserEvent && (
                  <Button
                    onPress={() =>
                      navigation.navigate(SCREENS.PENDING_ATTENDEES, {eventId: item!.event_id, eventName: item!.name})
                    }
                    label={t('events.requiring_confirmation')}
                    containerStyle={{
                      backgroundColor: colors.statusPurple,
                      borderRadius: 50,
                    }}
                    textStyle={{fontSize: 15, lineHeight: 16, fontWeight: '500', color: colors.white}}
                  />
                )}
              </View>
              <View style={{marginTop: 20}}>
                <TouchableOpacity
                  style={styles.attendeeContainer}
                  onPress={() => {
                    navigation.navigate(SCREENS.PERSONAL_INFO, {
                      user: {
                        tag: host?.uid + 'image',
                        source: host?.photo || '',
                        description: host?.description || '',
                        name: (host as Business)?.name
                          ? (host as Business)?.name
                          : (host as User)?.last_name
                            ? `${(host as User)?.first_name} ${(host as User)?.last_name || ''}`
                            : (host as User)?.first_name || '',
                        user_id: host?.uid || '',
                        eventName: fetchedItem?.name,
                      },
                    });
                  }}>
                  <View style={styles.attendeeLeftContainer}>
                    <View style={styles.attendeePhoto}>
                      <FastImage
                        style={StyleSheet.absoluteFillObject}
                        resizeMode={'cover'}
                        source={{uri: host?.photo, priority: 'normal'}}
                      />
                      <Animated.Image
                        sharedTransitionTag={host?.uid + 'image'}
                        source={{uri: host?.photo}}
                        style={styles.attendeePhoto}
                      />
                    </View>

                    <Text style={styles.attendeeName}>
                      {(host as Business)?.name
                        ? (host as Business)?.name
                        : (host as User)?.last_name
                          ? `${(host as User)?.first_name} ${(host as User)?.last_name || ''}`
                          : (host as User)?.first_name || ''}
                    </Text>
                    {/* !isUserEvent ? (
                      <View style={styles.textRowContainer}>
                        <Text>Verified Organiser</Text>
                        <Image
                          source={require('../../../assets/images/verified-badge.png')}
                          style={styles.verifiedIcon}
                        />
                      </View>
                    ) : (
                      <View />
                    ) */}
                    {!isUserEvent && (
                      <Button
                        onPress={() => onHostChatClick(host as Business)}
                        label={t('chat.chat')}
                        containerStyle={{
                          backgroundColor: colors.statusPurple,
                          borderRadius: 50,
                        }}
                        textStyle={{fontSize: 15, lineHeight: 16, fontWeight: '500', color: colors.white}}
                      />
                    )}
                  </View>
                </TouchableOpacity>
                {<View style={styles.divider} />}
              </View>
            </>
          )}

          <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
            <Text style={{fontSize: 18, lineHeight: 20, fontWeight: '600'}}>{t('events.attendees')}</Text>
            {false && isUserEvent && eventAttendees && eventAttendees!.length >= 1 && (
              <Button
                onPress={() => {
                  navigation.navigate(SCREENS.CHAT_STACK, {});
                }}
                label={t('chat.chat_all')}
                containerStyle={{
                  backgroundColor: colors.statusPurple,
                  borderRadius: 50,
                }}
                textStyle={{fontSize: 15, lineHeight: 16, fontWeight: '500', color: colors.white}}
              />
            )}
          </View>
          <View style={{marginTop: 20, marginBottom: 24}}>
            {item?.host_id != user?.uid && (
              <View style={styles.attendeeRowStyle}>
                <Image
                  style={styles.profileImg}
                  source={getLinkOf1stAttendeesProfile() ? {uri: getLinkOf1stAttendeesProfile()} : randomUserImg1}
                />
                <Image
                  style={[styles.profileImg, {left: -15}]}
                  source={getLinkOf2ndAttendeesProfile() ? {uri: getLinkOf2ndAttendeesProfile()} : randomUserImg2}
                />
                <Text style={styles.attendeesCount}>+ {getAttendeesCount()}</Text>
              </View>
            )}
            {item?.host_id === user?.uid && (
              <>
                {eventAttendees ? (
                  eventAttendees.map?.((attendee, index) => (
                    <React.Fragment key={index}>
                      <TouchableOpacity
                        style={styles.attendeeContainer}
                        disabled={!attendee.user}
                        onPress={() => {
                          navigation.navigate(SCREENS.PERSONAL_INFO, {
                            user: {
                              tag: index + 'image',
                              source: attendee.user.photo,
                              description: attendee.user.description,
                              name: `${attendee.user.first_name} ${attendee.user.last_name || ''}`,
                              user_id: attendee.user.uid,
                              eventName: fetchedItem?.name,
                            },
                          });
                        }}>
                        <View style={styles.attendeeLeftContainer}>
                          {attendee?.user && (
                            <View style={styles.attendeePhoto}>
                              <FastImage
                                style={StyleSheet.absoluteFillObject}
                                resizeMode={'cover'}
                                source={{uri: attendee?.user?.photo, priority: 'normal'}}
                              />
                              <Animated.Image
                                sharedTransitionTag={index + 'image'}
                                source={{uri: attendee?.user?.photo}}
                                style={styles.attendeePhoto}
                              />
                            </View>
                          )}

                          {attendee?.user ? (
                            <Text
                              style={
                                styles.attendeeName
                              }>{`${attendee?.user?.first_name} ${attendee?.user?.last_name}`}</Text>
                          ) : (
                            <Text style={[styles.attendeeName, {marginLeft: 0}]}>
                              {`${attendee?.name}`} <Text style={styles.attendeeSub}>{'(external)'}</Text>
                            </Text>
                          )}
                        </View>
                        {isUserEvent && (
                          <Button
                            label={t('generic.remove')}
                            onPress={async () => {
                              await deleteEventUserStatus({
                                event_id: eventId,
                                user_id: attendee.user?.uid,
                                email: attendee.email,
                              });
                            }}
                            containerStyle={styles.buttonRejectContainer}
                            textStyle={styles.buttonText}
                          />
                        )}
                      </TouchableOpacity>
                      {<View style={styles.divider} />}
                    </React.Fragment>
                  ))
                ) : (
                  <SkeletonPlaceholder>
                    <SkeletonPlaceholder.Item width={'100%'}>
                      <SkeletonPlaceholder.Item width={'100%'} height={45} borderRadius={8} marginBottom={8} />
                      <SkeletonPlaceholder.Item width={'100%'} height={45} borderRadius={8} marginBottom={8} />
                      <SkeletonPlaceholder.Item width={'100%'} height={45} borderRadius={8} marginBottom={8} />
                    </SkeletonPlaceholder.Item>
                  </SkeletonPlaceholder>
                )}
              </>
            )}
          </View>
        </View>
        <MapView
          ref={mapRef}
          style={{
            flex: 1,
            minHeight: 300,
            maxHeight: height * 0.85,
            marginHorizontal: 16,
            borderRadius: 16,
            marginBottom: 60,
          }}
          provider={PROVIDER_GOOGLE}
          showsMyLocationButton={false}
          showsCompass={false}
          toolbarEnabled={false}
          showsTraffic={false}
          showsBuildings={false}
          showsIndoors={false}
          customMapStyle={getMapStyle(isDarkMode)}
          initialRegion={{
            latitude: item?.coords?.lat || 51.5072178,
            longitude: item?.coords?.long || -0.1275862,
            latitudeDelta: 0.015,
            longitudeDelta: 0.015,
          }}
          showsUserLocation={true}
          scrollEnabled={true}
          zoomEnabled={true}
          pitchEnabled={true}
          rotateEnabled={true}>
          {Platform.OS == 'android' && (
            <Marker coordinate={coords}>
              <View style={styles.customMarkerContainer}>
                <View style={styles.markerContent}>
                  <Text style={styles.addressText}>{address}</Text>
                </View>
                <View style={styles.arrowContainer}>
                  <View style={styles.arrow} />
                </View>
              </View>
            </Marker>
          )}
          {Platform.OS == 'ios' && (
            <Marker
              coordinate={{
                latitude: item?.coords?.lat || 51.5072178,
                longitude: item?.coords?.long || -0.1275862,
              }}>
              <Image
                source={require('../../../../assets/images/pinEvent.png')}
                resizeMode="contain"
                style={{width: 35, height: 35, tintColor: colors.statusBlue}} // Customize the size here
              />
            </Marker>
          )}
        </MapView>
      </BottomSheetScrollView>
      <Calendar refRBSheet={calendarRef} recurrences={item?.recurrence_events || []} onJoinEvent={onJoinEvent} />

      <Dialog
        visible={pincodeDialogVisible}
        contentStyle={{padding: 0}}
        titleStyle={{
          alignSelf: 'flex-start',
          fontSize: 14,
          fontWeight: 'bold',
        }}
        title="Please enter post code"
        onTouchOutside={() => setPinCodeDialog(false)}
        onRequestClose={function (): void {}}
        contentInsetAdjustmentBehavior={undefined}>
        <View
          style={{
            paddingHorizontal: 15,
            paddingBottom: 15,
          }}>
          <TextInput
            placeholder="Enter post code"
            value={postCode}
            style={styles.textInput}
            onChangeText={setPostCode}
          />
          <Button
            label={'Continue'}
            containerStyle={{backgroundColor: colors.eventInfluencer, marginTop: 10}}
            textStyle={{color: colors.white}}
            onPress={onContinuePress}
          />
        </View>
      </Dialog>
      {
        <ModalWithJoin
          name={`${item?.name}`}
          description={`${item?.description}`}
          isVisible={modalIsVisible}
          close={() => setModalIsVisible(false)}
          payment_url={`${item?.payment_url}`}
          onPress={() => () => {
            addJoinEventStatus();
          }}
        />
      }
      <CommentSheet
        commentSheetRef={commentSheetRef}
        commentList={commentList ? commentList : []}
        onAddComment={onAddComment}
        photo={user?.photo}
        isUserEvent={isUserEvent}
      />
      {/* <ModalWithCalendar startDate={new Date(item?.start_date)} endDate={new Date(item?.end_date)} /> */}
      <AnimatedLoadingModal
        isVisible={isAnimationVisible}
        isButtonVisible={false}
        callback={() => setAnimationVisisble(false)}
        close={() => setAnimationVisisble(false)}
        isQRCode={true}
      />

      <IssueModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onTechnicalIssueClick={() => {
          console.log('Technical Issue Selected');
          onIssueTypeClick('t');
          setModalVisible(false);
        }}
        onEventIssueClick={() => {
          onIssueTypeClick('e');
          console.log('Event Issue Selected');
          setModalVisible(false);
        }}
      />
    </BottomSheetModalProvider>
  );
}
